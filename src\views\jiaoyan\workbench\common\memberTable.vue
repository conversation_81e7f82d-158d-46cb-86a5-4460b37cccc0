<template>
    <el-form class="myform" inline>
         <el-form-item>
            <el-input 
            class="definput" 
            placeholder="请输入名称"
            clearable
            v-model="pageBean.name"></el-input>
        </el-form-item>
        <el-form-item>
            <el-input 
            class="definput" 
            placeholder="请输入学校/单位名称"
            clearable
            v-model="pageBean.unitName"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button class="defbtn" :icon="Search" @click="searchAction">搜索</el-button>
        </el-form-item>
    </el-form>
    <el-table class="mytable" :data="tableData" style="margin-top: 20px;">
        <el-table-column prop="userName" label="名称" align="center" min-width="80px" />
        <el-table-column prop="age" label="年龄" align="center" width="80px" />
        <el-table-column prop="sex" label="性别" align="center" width="80px">
            <template #default="scope">
                {{sexMap[scope.row.sex] || '--'}}
            </template>
        </el-table-column>
        <el-table-column prop="unitType" label="属性" align="center" width="80px">
            <template #default="scope">
                {{unitTypeMap[scope.row.unitType] || '--'}}
            </template>
        </el-table-column>
        <el-table-column prop="unitName" label="高校/企业" align="center" width="183px"  show-overflow-tooltip />
        <el-table-column prop="departmentName" label="部门" align="center" min-width="183px" />
        <el-table-column prop="userTitle" label="职称" align="center" width="80px"/>
        <el-table-column v-if="type == 1" prop="createTime" label="申请时间" align="center" width="157px"  />
        <el-table-column v-if="type == 2" prop="roleType" label="权限" align="center" width="157px"  >
            <template #default="scope">
                {{ roleTypeMap[scope.row.roleType] }}
            </template>
        </el-table-column>
        <el-table-column fixed="right" prop="edit" label="操作" align="center" width="120px"  >
            <template v-if="type == 1" #default="scope">
                <el-button type="text" class="bbtn" @click="changeAction(scope.row,1)">同意</el-button>
                <el-divider direction="vertical" />
                <el-button type="text" class="bbtn" @click="changeAction(scope.row,2)">拒绝</el-button>
            </template>
            <template v-else-if="type == 2" #default="scope">
                <el-button :disabled="scope.row.isCreateBy" type="text" class="bbtn" @click="changeAction(scope.row,3)">授权</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
    v-model:current-page="pageBean.pageNum"
    v-model:page-size="pageBean.pageSize"
    :background="true"
    layout="total, prev, pager, next, jumper"
    :total="total"
    @current-change="handleCurrentChange"
    style="margin-top: 20px; justify-content: center;"/>
    <msg-dialog ref="msgdialogRef"/>
    <authdialog ref="authdialogRef" @reload="searchAction"/>
</template>

<script setup>
import {
  Plus,
  Search
} from '@element-plus/icons-vue'
import { 
    getResearchOfficeUsers,
    updateResarchofficeuser } from "@/api/expert/index";
import authdialog from './authdialog.vue';
import { onMounted, reactive } from "vue"
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
const sexMap = ref({
    0:'女',
    1:'男',
    2:'未知'
})
const unitTypeMap = ref({
    1:'高校',
    2:'企业'
})
const roleTypeMap = ref({
    1:'主理人',
    2:'助教',
    3:'一般用户'
})
const route = useRoute()
const msgdialogRef = ref()
const authdialogRef = ref()
const props = defineProps({
    type:{
        type:Number,
        default:1
    }
})
const tableData = ref([])
const total = ref(0)
let pageBean = reactive({
    pageNum:1,
    pageSize:10,
    name:'',
    unitName:'',
    status:props.type,
    researchOfficeId:route.query.id
})
const msgMap = ref({
    2:'该用户已加入教研室',
    3:'该用户已被拒绝加入教研室',
})
function changeUser(par,type){
    updateResarchofficeuser(par).then((result) => {
        if (result.data) {
            msgdialogRef.value.show({
                type:type == 2 ? 'reject' :'success',
                title:'提醒',
                msg:msgMap.value[par.status]
            })
            loadData()
        }else{
            ElMessage.error('操作失败')
        }
    }).catch((err) => {
        
    });
}
const changeAction = (data,type) =>{

    if (type == 1) {
        msgdialogRef.value.show({
            type:'edit',
            title:'提醒',
            msg:'是否允许该用户加入教研室?',
            submitBtnText:'同意',
            submitAction:()=>{
                changeUser({
                    id:data.id,
                    status:2
                },type)
            }
        })
    }else if (type == 2){
        msgdialogRef.value.show({
            type:'edit',
            title:'提醒',
            msg:'是否拒绝该用户加入教研室??',
            submitBtnText:'拒绝',
            submitAction:()=>{
                changeUser({
                    id:data.id,
                    status:3
                },type)
            }
        })
    }else if( type == 3){
        authdialogRef.value.show(data)
    }

}
const loadData = () =>{
    getResearchOfficeUsers(pageBean).then((result) => {
        tableData.value = result.data || []
        total.value = result.page.total
    }).catch((err) => {
        
    });
}
const searchAction = () =>{
    pageBean.pageNum = 1
    loadData()
}
const handleCurrentChange = (page) =>{
    pageBean.pageNum = page
    loadData()
}
onMounted(()=>{
    loadData()
})
</script>

<style lang="scss" scoped>
.myform{
    ::v-deep(.el-form-item){
        margin-right: 12px;
    }
}
.definput{
    width: 280px !important;
    height: 36px !important;
}
.mytable{
    margin-top: 0px !important;
}
</style>