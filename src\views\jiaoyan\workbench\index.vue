<template>
  <div class="mainbg w1280">
    <back>返回</back>
    <div class="maskbg">
        <div class="leftdiv">
            <router-link 
            class="navTrain"  
            :to="{
              path:'/jiaoyanshi/workbench/member',
              query:route.query
            }" 
            tag="div">人员审核</router-link>
            <router-link 
            class="navTrain"  
            :to="{
              path:'/jiaoyanshi/workbench/activity',
              query:route.query
            }"
            tag="div">活动管理</router-link>
        </div>
        <div class="rightdiv">
            <router-view />
        </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';

const route = useRoute()
</script>

<style lang="scss" scoped>
.mainbg{
    min-height: calc(100vh - 200px) !important;
    padding: 20px;
}
.maskbg{
    margin-top: 20px;
    min-height: calc(100vh - 235px) !important;
    display: flex;
    justify-content: space-between;
}
.navTrain {
  text-decoration: none;
  display: block;
  font-size: 14px;
  width: 158px;
  height: 40px;
  line-height: 40px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #717680;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding: 0px 12px;
  margin-bottom: 10px;
  i {
    margin-right: 12px;
    line-height: 46px;
  }
  &:hover {
    background-color: #EBF0FF;
    border-radius: 4px;
    color: #386CFC;
    cursor: pointer;
  }
}
.router-link-active {
  background-color: #EBF0FF;
  border-radius: 4px;
  color: #386CFC;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #386CFC;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.maincontent .el-row {
  margin: 0 !important;
}
.leftdiv{
    padding: 20px 16px;
    background-color: white;
    border-radius: 12px;
}
.rightdiv{
    width: calc(100% - 210px);
    min-width: calc(100% - 210px);
    min-height: 100%;
    background-color: white;
    border-radius: 12px;
}
</style>