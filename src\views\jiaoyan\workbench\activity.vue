<template>
  <div class="bg">
    <div class="titlediv">
      <div class="title">活动管理</div>
      <el-button class="defbtn" :icon="Plus" @click="showAdd">新增活动</el-button>
    </div>
    <el-form class="searchdiv" inline>
      <el-form-item>
        <el-input 
        class="definput" 
        placeholder="请输入活动名称"
        clearable
        v-model="pageBean.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select 
        class="definput" 
        v-model="pageBean.type" 
        clearable
        placeholder="请选择活动类型">
          <el-option 
          v-for="item in activityTypes" 
          :key="item.id" 
          :label="item.name" 
          :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="defbtn" :icon="Search" @click="searchAction">搜索</el-button>
      </el-form-item>
    </el-form> 
    <div class="listdiv" v-if="tableData.length>0">
      <activityitem 
      v-for="item in tableData" 
      :key="item"
      :item="item"
      @reload="reloadAction"></activityitem>
    </div>
    <empty-state class="mt" v-else />
    <el-pagination
      v-model:current-page="pageBean.pageNum"
      v-model:page-size="pageBean.pageSize"
      :background="true"
      layout="total, prev, pager, next, jumper"
      :total="total"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; justify-content: center;"/>
  </div>
</template>

<script setup>
import {
  Plus,
  Search
} from '@element-plus/icons-vue'
import { 
  getActivityList,
  deleteActivity
} from '@/api/expert/index'
import EmptyState from '@/components/common/EmptyState.vue';
import {
     labelList, 
} from '@/api/api'
import { onMounted, reactive } from 'vue';
import activityitem from './common/activityitem.vue';
import { ElMessage } from 'element-plus';
import { activityTypes,activityTypesMap } from '@/utils/static-tools';
import { useRouter,useRoute } from 'vue-router';
const router = useRouter()
const route = useRoute()
const pageBean = reactive({
  pageNum:1,
  pageSize:6,
  researchOfficeId:route.query.id,
  name:'',
  type:'',
})
const total = ref(0)
const tableData = ref([])

const loadData = () =>{
  getActivityList(pageBean).then(res => {
      tableData.value = res.data
      total.value = res.page.total
  })
}
const searchAction = () =>{
  pageBean.pageNum = 1;
  loadData()
}
const handleCurrentChange = (page) =>{
  pageBean.pageNum = page;
  loadData()
}
const showAdd = () =>{
  router.push({
    path:'/jiaoyanshi/addActivity',
    query:{
      researchOfficeId:route.query.id
    }
  })
}
const reloadAction = ()=>{
  pageBean.pageNum = 1;
  loadData()
}
onMounted(()=>{
  loadData()
})
</script>

<style lang="scss" scoped>
.bg{
  padding: 30px;
}
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.searchdiv{
  margin-top: 20px;
  ::v-deep(.el-form-item){
    margin-right: 12px;
  }
}
.definput{
  width: 280px;
  height: 36px;
  border-radius: 4px;
  :v-deep(.el-input__inner){
    border: 1px solid #E1E4EB;
  }
  ::v-deep(.el-select__wrapper){
    min-height: 36px !important;
  }
}
.listdiv{
  display: flex;
  flex-wrap: wrap;
  height: 540px;
  .activity{
    width: calc(25% - 15px);
    margin-right: 20px;
  }
  .activity:nth-child(3n){
    margin-right: 0px;
  }
}

.topBtn{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}
.mt{
  margin: calc(30% - 100px);
}
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>