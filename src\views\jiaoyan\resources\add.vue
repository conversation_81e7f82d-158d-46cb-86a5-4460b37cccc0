<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <el-card class="card">
                <div class="title">{{ isEdit ? '编辑资源' : '资源上传' }}</div>
                <el-form
                    ref="formRef"
                    :model="form"
                    :rules="rules"
                    label-width="120px"
                    class="resource-form"
                >
                    <el-form-item label="资源名称：" prop="resourceName">
                        <el-input
                            v-model="form.resourceName"
                            placeholder="请输入资源名称"
                            clearable
                            class="w-full"
                        />
                    </el-form-item>
                   <el-form-item label="主讲人：" prop="speaker">
                        <el-input
                            v-model="form.speaker"
                            placeholder="请输入主讲人"
                            clearable
                            class="w-full"
                        />
                    </el-form-item>

                    <el-form-item label="资源类型：" prop="resourceTypeId">
                        <el-select
                            v-model="form.resourceTypeId"
                            placeholder="请选择资源类型"
                            clearable
                            class="w-full"
                        >
                            <el-option
                                v-for="item in resourceTypes"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="资源标签：" prop="label">
                        <el-select
                            v-model="form.label"
                            placeholder="请选择资源标签"
                            clearable
                            class="w-full"
                        >
                            <el-option
                                v-for="item in resourceTags"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>



                    <el-form-item label="更多介绍：" >
                        <editor ref="editorRef" />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleSubmit" :loading="submitLoading" class="confirm">
                            {{ isEdit ? '确认更新' : '确认上传' }}
                        </el-button>
                        <el-button @click="handleCancel" class="cancel">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'

import editor from '@/components/editor.vue'
import { getResTypeList, saveTeachingResource, updateTeachingResource, getResourceInfo } from '@/api/expert/index'

const router = useRouter()
const route = useRoute()
const formRef = ref()
const editorRef = ref()
const submitLoading = ref(false)
const isEdit = ref(false)
const resourceId = ref(null)



const resourceTypes = ref([])

const resourceTags = ref([
  { id: 1, name: '教学资源' },
  { id: 2, name: '教研成果' },
  { id: 3, name: '教研资料' }
])

const form = ref({
  resourceName: '',
  speaker: '',
  resourceTypeId: '',
  label: '',
  description: ''
})

const rules = ref({
  resourceName: [
    { required: true, message: '请输入资源名称', trigger: 'blur' }
  ],
  speaker: [
    { required: true, message: '请输入主讲人', trigger: 'blur' }
  ],
  resourceTypeId: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  label: [
    { required: true, message: '请选择资源标签', trigger: 'change' }
  ],

})

const handleSubmit = () => {
  // 获取富文本编辑器内容
  if (editorRef.value) {
    form.value.description = editorRef.value.getContentHtml
  }

  formRef.value.validate((valid) => {
    if (!valid) return
    
    submitLoading.value = true

    const submitData = {
      ...form.value,
      researchOfficeId: route.query.id
    }

    // 编辑模式需要传递ID
    if (isEdit.value && resourceId.value) {
      submitData.id = resourceId.value
    }

    const apiCall = isEdit.value ? updateTeachingResource(submitData) : saveTeachingResource(submitData)

    apiCall.then(res => {
      submitLoading.value = false
      if (res.status === 0) {
        ElMessage.success(isEdit.value ? '资源更新成功！' : '资源保存成功！')
        router.push('/jiaoyanshi/resources')
      } else {
        ElMessage.error(res.message)
      }
    })
  })
}


const handleCancel = () => {
  router.push('/jiaoyanshi/resources')
}



const getResourceTypes = () => {
  const params = {
    researchOfficeId: route.query.id,
    pageNum: 1,
    pageSize: 10
  }
  getResTypeList(params).then(res => {
    if (res.status == 0) {
      resourceTypes.value = res.data
    }
  })
}

const loadResourceDetail = () => {
  if (!resourceId.value) return
  
  getResourceInfo(resourceId.value).then(res => {
    if (res.status !== 0 || !res.data) return
    
    const data = res.data
    Object.assign(form.value, data)

    // 设置富文本编辑器内容
    if (editorRef.value && data.description) {
      editorRef.value.setContentHtml(data.description)
    }

    // 处理资源文件
    if (data.resourceUrl) {
      form.value.resourceFiles = [data.resourceUrl]
    }

    // 处理封面图片
    if (data.coverUrl) {
      form.value.coverImage = [data.coverUrl]
    }
  })
}

onMounted(() => {
  if (route.query.resourceId) {
    isEdit.value = true
    resourceId.value = route.query.resourceId
  }

  getResourceTypes()

  if (isEdit.value) {
    loadResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}

.card {
  margin-top: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.resource-form {
  max-width: 800px;

  .w-full {
    width: 365px;
    height: 42px;
  }


  .upload-tip {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #878D99;
    line-height: 1.4;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-textarea {
    .el-textarea__inner {
      resize: vertical;
    }
  }
}
:deep(){
  .el-form-item__label{
    padding-right: 0 !important;
  }
  .el-select__wrapper{
    height: 42px;
  }
  .el-upload-list__item{
    height: 31px;
    background: #F5F7FA;
    border-radius: 4px 4px 4px 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
  }
}

.chess{
  width: 136px;
  height: 42px;
  background: #386CFC;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 8px;
}
.cancel{
  width: 120px;
  height: 42px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #386CFC;
  margin-left: 16px;
}
.confirm{
  width: 120px;
  height: 42px;
  background: #386CFC;
  border-radius: 4px 4px 4px 4px;
}
</style>