<template>
    <el-dialog 
    width="493px" 
    :title="dialogTitle" 
    v-model="dialogVisible" 
    class="adddialog"
    @close="close" 
    :close-on-click-modal="false"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }">
        <el-form 
        :rules="rules" 
        ref="addform" 
        :model="form" 
        label-position="top">
                <el-form-item label="用户权限" prop="roleType">
                    <el-select v-model="form.roleType" placeholder="请选择用户权限" class="wid100">
                        <el-option
                        v-for="item in auths"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        <template #footer>
            <div class="dialog-footer">
                 <el-button class="linebtn" @click="close">取消</el-button>
                 <el-button class="defbtn40" @click="onSubmit">同意</el-button>
            </div>
        </template>
    </el-dialog>
    <msg-dialog ref="msgdialogRef"/>
</template>

<script setup>
import { updateResarchofficeuser } from "@/api/expert/index";
import { useRoute } from "vue-router";
import { ElMessage, roleTypes } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const auths = ref([
    {
        label:'主理人',
        value:1
    },
    {
        label:'助理',
        value:2
    },
    {
        label:'一般用户',
        value:3
    }
])
const dialogVisible = ref(false)
const msgdialogRef = ref()
const dialogTitle = ref('授权')
let form = reactive({
    id: '',
    roleType:undefined
})
const rules = ref({
    roleType: [
        { required: true, message: '请选择用户权限', trigger: 'change' }
    ]
})
const emits = defineEmits(['reload'])
const addform = ref()
const show = (data) =>{
    dialogVisible.value = true;
    dialogTitle.value = '授权'
    form.id = data.id
    form.roleType = data.roleType
}
function resetForm() {
    for (let key in form) {
        form[key] = '';
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
const roleTypeMap = ref({
    1:'主理人',
    2:'助教',
    3:'一般用户'
})
function onSubmit() {
    addform.value.validate((valid) => {
        if (valid) {
            updateResarchofficeuser(form).then(res => {
                if (res.status == 0) {
                    dialogVisible.value = false;
                    msgdialogRef.value.show({
                        type:'success',
                        title:'提醒',
                        msg:'该用户已被授权为'+`“${roleTypeMap.value[form.roleType]}”`,
                        onClose:()=>{
                            emits('reload')
                        }
                    })
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}

onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
<style lang="scss">
</style>