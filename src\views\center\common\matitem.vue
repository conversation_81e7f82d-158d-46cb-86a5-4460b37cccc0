<template>
  <div class="matitem">
    <img :src="item.coverUrl" alt="">
    <el-text class="title" truncated tag="div">{{ item.name }}</el-text>
    <div class="timecss"><img src="@/assets/timeicon.png" alt="">2025-07-26 10:31:22</div>
  </div>
</template>

<script setup>
const props = defineProps({
    item:{
        type:Object,
        default:()=>{
            return new Object()
        }
    }
})
</script>

<style lang="scss" scoped>
.matitem{
    img{
        width: 100%;
        aspect-ratio: 192/254;
        border-radius: 4px;
    }
    .title{
        height: 24px;
        font-family: Source <PERSON>, Source <PERSON>;
        font-weight: 500;
        font-size: 16px;
        color: #2D2F33;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    margin-bottom: 20px;
}
.timecss{
    img{
        width: 16px;
        height: 16px;
        top: 3px;
        right:2px;
    }
    height: 20px;
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    text-align: left;
    font-style: normal;
    text-transform: none;
}
</style>