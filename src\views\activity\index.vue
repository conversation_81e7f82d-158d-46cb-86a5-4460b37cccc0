<template>
  <div class="mdivcss">
    <div class="wid bgcss">
      <back>返回</back>
      <div class="bgmain">
        <div class="headbox">
          <img class="cover" src="../../assets/1.png" alt="" />
          <div class="infobox">
            <p class="title">剑指Java面试，高薪Offer面试技巧</p>
            <p class="time">活动时间：2025年07月31日 10:30~2025年31日 18:00</p>
            <p class="people"><span>主讲人：</span><span>周雨楠</span></p>
            <p class="text">
              《你只是来体验生命的》4：文章传达了一种豁达、包容的生命态度。作者通过自身的经历和感悟，告诉读者生命有多种形态，我们不必被外界的刻板印象所束缚，应该勇敢地去体验各种形式的喜悦、伤心、难过、焦虑，为自己的生命负责。对于三十岁的女生来说，这种观念尤为重要，它鼓励女生们在人生的这个阶段，走出舒适区，去探索更多的可能性，追求自己真正想要的生活，让自己的幸福快乐的生活下回发很快就的生活下回发很快就风格风格和
            </p>
            <el-button type="primary" class="signup" @click="showDialog"
              >立即报名</el-button
            >
            <!-- <div class="greybutton">立即报名</div> -->
          </div>
        </div>
        <div class="videobox">
          <div class="nostart">
            <div class="tips">
              <img src="@/assets/tan.png" alt="" />
              <span>请报名后参与活动</span>
            </div>
          </div>
          <!-- <div
            class="palyvideo player-wrapper"
            id="playerLive"
            ref="playerContainer"
          ></div> -->
          <div class="mark">活动回放</div>
          <div class="videoflex">
            <div
              @click="playHandle(index)"
              class="videoitem"
              v-for="(item, index) in videoList"
              :class="{ activeCss: index == currentIndex }"
            >
              <img
                v-if="index == currentIndex"
                class="icon"
                src="../../assets/activeplay.png"
                alt=""
              />
              <img v-else class="icon" src="../../assets/playb.png" alt="" />
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="contentbox"></div>
      </div>
    </div>
    <baomingDialog ref="dialogRef"></baomingDialog>
  </div>
</template>

<script setup>
import back from '@/components/back.vue'
import baomingDialog from '@/components/baomingDialog.vue'
const dialogRef = ref('')
function showDialog() {
  dialogRef.value.open()
}
const playerInstance = ref(null)
// 初始化播放器
const initPlayer = async (videoUrl) => {
  try {
    if (playerInstance.value) {
      playerInstance.value.dispose()
    }
    const playerConfig = {
      id: 'playerLive',
      source: videoUrl,
      width: '100%',
      height: '686px',
      autoplay: false,
    }
    playerInstance.value = new Aliplayer(playerConfig, (player) => {
      player.on('play', () => {})
    })
  } catch (err) {
    console.error('初始化播放器失败:', err)
  }
}
onMounted(() => {
  initPlayer(
    'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/goldcourse/manager/2024092307/第3课 自我认知.mp4'
  )
})

const currentIndex = ref(0)

const videoList = ref([
  {
    name: '回放P1：夏季教研项目活动回放P1：夏季教研项目活动回放P1：夏季教研项目活动',
    url: 'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/goldcourse/web/1145525338994906575.mp4',
  },
  {
    name: '回放P1：夏季教研项目活动',
    url: 'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/goldcourse/manager/2024092307/第3课 自我认知.mp4',
  },
])

function playHandle(index) {
  currentIndex.value = index
  initPlayer(videoList.value[index].url)
}
</script>

<style lang="scss" scoped>
.tips {
  width: 160px;
  height: 36px;
  background: rgba(243, 78, 78, 0.12);
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  line-height: 36px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #f34e4e;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nostart {
  height: 400px;
  background: rgba($color: #000000, $alpha: 1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.activeCss {
  background: #386cfc !important;
  color: #fff !important;
}
.palyvideo {
  height: 800px;
}
.icon {
  margin-right: 8px;
}
.videoitem {
  width: 396px;
  height: 40px;
  background: #f5f7fa;
  border-radius: 8px 8px 8px 8px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 16px;
  margin-top: 16px;
  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: 1;
  }
}
.videoitem:nth-child(3n) {
  margin-right: 0;
}
.videoflex {
  display: flex;
  flex-wrap: wrap;
}
.mark {
  width: 136px;
  height: 45px;
  background: #ebf0ff;
  border-radius: 0px 12px 12px 0px;
  text-align: center;
  line-height: 45px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #386cfc;
  left: -30px;
  position: relative;
  margin-top: 40px;
}
.videobox {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  margin-top: 20px;
  padding: 30px;
  position: relative;
}
.contentbox {
  padding: 30px;
  margin-top: 20px;
  background: #fff;
  border-radius: 8px 8px 8px 8px;
}
.greybutton {
  width: 112px;
  height: 36px;
  background: #b4bccc;
  border-radius: 99px 99px 99px 99px;
  text-align: center;
  line-height: 36px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #ffffff;
}
.signup {
  margin-top: 16px;
  width: 112px;
  height: 36px;
  background: #386cfc;
  border-radius: 99px 99px 99px 99px;
}
.text {
  margin-top: 12px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #44474d;
  line-height: 32px;
  text-align: justify;
}
.people {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #878d99;
  margin-top: 8px;
}
.time {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #878d99;
  margin-top: 12px;
}
.title {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2d2f33;
}
.infobox {
}
.cover {
  width: 640px;
  height: 360px;
  margin-right: 30px;
}
.headbox {
  padding: 30px;
  display: flex;
  background: #fff;
  border-radius: 8px 8px 8px 8px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}
.tag {
  width: 76px;
  height: 27px;
  background: #f5f7fa;
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  line-height: 27px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #386cfc;
  margin-right: 30px;
}
.wid {
  width: 1280px;
}
.bgcss {
  margin: 0px auto;
  margin-bottom: 40px;
}
.mdivcss {
  padding: 20px 0px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 93px);
}
.bgmain {
  margin-top: 20px;
}
.contentdiv {
  margin: 0 auto;
  min-height: 441px;
  background: #fff;
  padding: 30px;
}
.zititle {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  line-height: 34px;
  text-align: center;
  margin-bottom: 17px;
}
.timetext {
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  color: #666;
}
.line {
  width: 100%;
  height: 1px;
  background: #e5e5e5;
  margin: 20px 0;
}
.cssfont {
  margin-top: 30px;
  font-size: 16px;
  color: #4b4d4b;
  line-height: 32px;
  text-align: justify;
  display: -webkit-box;
  overflow-wrap: break-word;
  overflow: hidden;
  -webkit-box-orient: vertical;
}
</style>s