import service from '@/utils/request.js'

// 教研室教材列表
export function getMaterialList(data) {
  return service.request({
    method: 'get',
    url: '/aigc/controller/materialinfo/list',
    params: data
  });
}
//新增教研室
export function saveMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/materialinfo/save',
    data,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

//编辑教研室
export function updateMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/materialinfo/update',
    data,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除教研室
export function deleteMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/materialinfo/delete',
    data,
  });
}
// 获取教材信息
export function getMaterialInfo(params) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/materialinfo/info/${id}`,
    params
  });
}
// 