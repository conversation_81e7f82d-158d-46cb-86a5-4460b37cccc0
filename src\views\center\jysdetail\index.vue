<template>
  <div>
    <div class="bg">
      <div class="main">
        <div class="maincontent">
          <el-row :gutter="10">
            <el-col :span="24">
              <div class="grid-content bg-purple">
                <div class="listNav">
                  <div class="main">
                    <span>当前位置：</span>
                    <span class="bcolor" @click="goHome">首页</span>
                    <span> > </span>
                    <span class="sectext" @click="goJYS">个人中心</span>
                    <span> > </span>
                    <span>我的教研室</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col
              class="minhei"
              :span="24"
              style="
              "
            >
                <div class="maindiv">
                  <div class="leftdiv">
                    <div class="touxiang">
                      <div class="imgContent">
                        <img
                          v-if="useUserStore.getPicUrl"
                          :src="useUserStore.getPicUrl"
                          alt
                        />
                        <img v-else src="@/assets/touxiang1.jpg" alt="" />
                      </div>
                      <div class="teaNickname" v-if="useUserStore.nickname">
                        {{ useUserStore.nickname }}
                      </div>
                      <div class="teaNickname" v-else>教师职称</div>
                    </div>
                    <router-link 
                    class="navTrain" 
                    :to="{
                      path:'/center/jysdetail/material',
                      query:route.query
                    }" 
                    tag="div">
                      数字教材
                    </router-link>
                    <router-link 
                    class="navTrain" 
                    :to="{
                      path:'/center/jysdetail/basicdata',
                      query:route.query
                    }" 
                    tag="div"
                      >
                      数据管理</router-link
                    >
                    <router-link
                      class="navTrain"
                      :to="{
                        path:'/center/jysdetail/restype',
                        query:route.query
                      }"
                      tag="div"
                      >
                      资源分类管理</router-link
                    >
                    <router-link
                      class="navTrain"
                      :to="{
                        path:'/center/jysdetail/banner',
                        query:route.query
                      }"
                      tag="div"
                      >
                      轮播图管理</router-link
                    >
                  </div>
                  <div class="rightdiv"
                  >
                    <router-view></router-view>
                  </div>
                </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { userStore } from '@/stores/user.js'
import { useRoute,useRouter } from 'vue-router';
const route = useRoute()
const router = useRouter()
const useUserStore = userStore()
const goHome = () =>{
  router.push('/home')
}
const goJYS = () =>{
  router.push('/center/jiaoyan')
}
</script>
<style lang="scss" >
.sectext{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #44474D;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;
}
.invite_input {
  .el-input {
    .el-input__inner {
      height: 34px;
      width: 350px;
      line-height: 34px;
      background-color: #eeeeee;
      border: 0;
    }
  }
}
.information {
  .el-form-item__label {
    padding-right: 20px !important;
  }
  .el-form-item {
    margin-bottom: 15px !important;
    .el-input {
      width: 317px;
    }
    .el-input--suffix {
      width: 317px;
    }
  }
}
</style>
<style lang="scss" scoped>
.pathicon{
  width: 24px;
  height: 24px;
  // right: 4px;
  top: 6px;
}
.minhei {
  min-height: calc(100vh - 86px) !important;
  background-color: transparent;
}
.listNav {
  padding: 20px 0px;
  span {
    font-size: 14px;
    color: rgba(102, 102, 102, 1);
    line-height: 42px;
    margin-right: 3px;
  }
  :nth-child(3) {
    color: #ed7227;
  }
}
.bg {
  background: linear-gradient( 90deg, #F5F8FC 0%, #F7F5FC 100%);
  margin-top: 0px;
}
.maincontent {
}
.maindiv{
  display: flex;
  min-height:calc(100% - 60px);
  .leftdiv{
    width: 240px;
    background-color: #FFFFFF;
    min-height:100%;
    padding: 20px;
    border-radius: 8px;
    margin-right: 20px;
  }
  .rightdiv{
    padding: 20px;
    width: 100%;
    min-width: 100%;
    min-height:100%;
    border-radius: 8px;
    background-color: #FFFFFF;
  }
}
.touxiang {
  overflow: hidden;
  font-size: 0;
  text-align: center;
  margin-bottom: 28px;
  .imgContent {
    height: 76px;
    width: 76px;
    border: 1px solid #e6e6e6;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
      
    }
  }
  .teaNickname {
    width: 100%;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #222222;
    text-align: center;
    line-height: 16px;
    margin-top: 16px ;
  }
}
.separate {
  height: 10px;
  background-color: rgba(248, 248, 248, 1);
}
.navTrain {
  text-decoration: none;
  display: block;
  font-size: 14px;
  width: 160px;
  height: 40px;
  line-height: 40px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #717680;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding: 0px 12px;
  margin-bottom: 10px;
  i {
    margin-right: 12px;
    line-height: 46px;
  }
  &:hover {
    background-color: #EBF0FF;
    border-radius: 4px;
    color: #386CFC;
    cursor: pointer;
  }
}
.router-link-active {
  background-color: #EBF0FF;
  border-radius: 4px;
  color: #386CFC;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #386CFC;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.maincontent .el-row {
  margin: 0 !important;
}
.maincontent .main {
  margin-left: 0px;
  width: 810px;
}
.maincontent .main span {
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  line-height: 30px;
}
.bcolor{
  color: #386CFC !important;
}
</style>
