import service from '@/utils/request.js'

//新增数据管理
export function saveBanner(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/advertisingspace/save',
    data,
  });
}
export function updateBanner(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/advertisingspace/update',
    data,
  });
}
export function deleteBanner(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/advertisingspace/delete',
    data,
    type:2,
  });
}
// 根据教研室Id查询轮播图
export function getBannerList(params) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/advertisingspace/list`,
    params
  });
}

// 获取数据信息
export function getBannerInfo(id) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/advertisingspace/info/${id}`,
  });
}

