<template>
    <div class="bg">
        <div class="w1280">
            <div class="userBox">
                <div class="userInfo">
                    <img src="@/assets/touxiang.jpg" alt="" class="userLogo">
                    <div class="info">
                        <div class="userName">{{ userInfo.userName }}</div>
                        <div class="unit">{{ userInfo.unitName }}</div>
                        <div class="numberBox">
                            <div class="guanzhu"><span class="number">213</span>关注</div>
                            <div class="fensi"><span class="number">213</span>粉丝</div>
                        </div>
                        <div class="btnBox">
                            <el-button type="primary" class="btn">关注</el-button>
                            <el-button type="primary" class="btn">私信</el-button>
                        </div>
                    </div>
                </div>
                <div class="userContent">{{ userInfo.introduce }}</div>
            </div>


            <div class="title">TA的数字教材</div>
            <div class="bookList">
                <div class="bookItem" v-for="(item,index) in bookList" :key="index">
                    <img :src=item.img alt="" class="bookImg">
                    <div class="bookInfo">
                        <div class="bookName text-ellipsis-1">{{ item.name }}</div>
                        <div class="desc"><span class="label">主编：</span><span class="content">{{ item.author }}</span></div>
                        <div class="desc"><span class="label">ISBN：</span><span class="content">{{ item.isbn }}</span></div>
                        <div class="desc"><span class="label">出版社：</span><span class="content">{{ item.press }}</span></div>
                        <div class="desc"><span class="label">价格：</span><span class="price">￥{{ item.price }}</span></div>
                    </div>
                </div>
            </div>


            <div class="title">TA的资源</div>
            <div class="boxList">
                <div class="boxItem" v-for="(item,index) in boxList" :key="index">
                    <div class="topImg">
                        <img :src=item.img alt="" class="boxImg">
                    </div>
                    <div class="bottomInfo">
                        <div class="boxName text-ellipsis-1">{{ item.name }}</div>
                        <div class="booxTime">
                            <div class="user">
                                <img src="@/assets/user.png" alt="" class="userImg">
                                <div class="chief">{{ item.chief }}</div>
                            </div>
                            <div class="user">
                                <img src="@/assets/time.png" alt="" class="userImg">
                                <span class="time">{{ item.time }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <el-pagination
                v-model:current-page="pageParams.pageNum"
                v-model:page-size="pageParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                style="margin-top: 20px; justify-content: center;"
            />
        </div>
    </div>
</template>
<script setup>
import { getResarchOfficeUserInfo } from '@/api/expert/index'
import { useRoute } from 'vue-router'

const route = useRoute()
const userInfo = ref({})
const pageParams = ref({
    pageNum: 1,
    pageSize: 10
})
const total = ref(0)
const bookList = ref([
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'幼儿园教育活动设计与施计与施计与施',
        author:'张雪梅',
        isbn:'1578645564554',
        press:'江西高校出版社',
        price:'39.90'
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'幼儿园教育活动设计与施计与施计与施',
        author:'张雪梅',
        isbn:'1578645564554',
        press:'江西高校出版社',
        price:'39.90'
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'幼儿园教育活动设计与施计与施计与施',
        author:'张雪梅',
        isbn:'1578645564554',
        press:'江西高校出版社',
        price:'39.90'
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'幼儿园教育活动设计与施计与施计与施',
        author:'张雪梅',
        isbn:'1578645564554',
        press:'江西高校出版社',
        price:'39.90'
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'幼儿园教育活动设计与施计与施计与施',
        author:'张雪梅',
        isbn:'1578645564554',
        press:'江西高校出版社',
        price:'39.90'
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'幼儿园教育活动设计与施计与施计与施',
        author:'张雪梅',
        isbn:'1578645564554',
        press:'江西高校出版社',
        price:'39.90'
    },
]);
const boxList = ref([
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
    {
        img:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/wth-digital/2025/04/14/912f526d-8336-428f-bdbe-0abe8d169b09.png',
        name:'高等数学',
        chief:'周远安',
        time:'2024-09-06',
    },
]);
const getUserInfo = () => {
    getResarchOfficeUserInfo(route.query.id).then(res => {
        userInfo.value = res.data
    })
}
onMounted(() => {
    getUserInfo()
})
const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
};
</script>
<style lang="scss" scoped>
.bg{
    background: linear-gradient( 90deg, #F5F8FC 0%, #F7F5FC 100%);
    padding: 31px;
}
.userBox{
    padding: 20px;
    display: flex;
    height: 170px;
    background: #FFFFFF;
    border-radius: 12px 12px 12px 12px;
}
.userInfo{
    min-width: 370px;
    border-right: 1px solid #E1E4EB;
    display: flex;
    padding-right: 40px;
}
.userLogo{
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 3px solid rgba(255,255,255,0.6);
    margin-right: 20px;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
}
.unit{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
    margin-top: 2px;
    margin-bottom: 8px;
}
.numberBox{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}
.guanzhu{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
    margin-right: 16px;
}
.fensi{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.number{
    font-weight: 500;
    color: #2D2F33;
}
.btn{
    height: 32px;
    background: #386CFC;
    border-radius: 99px;
}
.userContent{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #44474D;
    line-height: 32px;
    padding-left: 40px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    margin-top: 20px;
    margin-bottom: 20px;
}
.bookList{
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(3, 1fr);
}
.bookItem{
    width: 413px;
    height: 218px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 16px;
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;
}
.bookItem:hover{
    transform: translateY(-8px);
    transition: all 0.3s ease;
    box-shadow: 0px 2px 16px 0px rgba(0,22,85,0.12);
}
.bookImg{
    width: 140px;
    height: 186px;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #E1E4EB;
    margin-right: 20px;
}
.bookName{
    width: 221px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2D2F33;
}
.desc{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
    margin-top: 16px;
    display: flex;
    align-items: center;
}
.label{
    display: inline-block;
    width: 60px;
    flex-shrink: 0;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
}
.price{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #FF8D24;
}
.boxList{
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(5, 1fr);
}
.boxItem{
    width: 240px;
    height: 215px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}
.boxItem:hover{
    transform: translateY(-8px);
    transition: all 0.3s ease;
    box-shadow: 0px 2px 16px 0px rgba(0,22,85,0.12);
}
.boxImg{
    width: 100%;
    height: 135px;
}
.bottomInfo{
    padding: 12px 8px;
}
.booxTime{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.user{
  display: flex;
  align-items: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
font-weight: 400;
font-size: 13px;
color: #878D99;
}
.userImg{
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.boxName{
    width: 224px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2D2F33;
    margin-bottom: 8px;
}
</style>
