<template>
  <div class="activity">
    <img :src="item.coverUrl" alt="">
    <div class="titlediv">
        <el-text truncated class="title">{{item.name}}</el-text>
        <el-dropdown
         :hide-on-click="true"
         trigger="click"
        @command="$event =>handleCommand($event,item)">
            <img src="@/assets/center/mbtnicon.png" alt="">
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item :command="1">编辑</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="3">开始直播</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="4">结束直播</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="5">报名信息</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="6">回放管理</el-dropdown-item>
                    <el-dropdown-item :command="2">删除</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
    <div class="msgdiv">
        <el-text truncated tag="p">活动类型：{{activityTypesMap[item.type]}}</el-text>
        <el-text truncated tag="p">创建时间：2025-07-31 12:22:43</el-text>
    </div>
    <span :class="activityStatusColorMap[item.status]" class="tagcss">{{ activityStatusMap[item.status] }}</span>
    <msg-dialog ref="msgdialogRef" />
    <addPlanback ref="addplanbackRef"/>
    <userjoin ref="userjoinRef" />
    <live-stream ref="liveStreamRef" />
  </div>
</template>

<script setup>
import { deleteActivity } from "@/api/expert/index";
import { activityTypesMap,activityStatusColorMap,activityStatusMap } from "@/utils/static-tools";
import addPlanback from './addPlanback.vue';
import userjoin from './userjoin.vue';
import liveStream from './liveStream.vue';
import { useRouter } from "vue-router";
import { dataType } from "element-plus/es/components/table-v2/src/common";
import { ElMessage } from "element-plus";
const router = useRouter()
const props = defineProps({
    item:{
        type:Object,
        default:()=>{
            return new Object()
        }
    }
})
const emits = defineEmits(['reload'])
const addplanbackRef = ref()
const msgdialogRef = ref()
const userjoinRef = ref()
const liveStreamRef = ref()

function deleteAction(data){
    deleteActivity({
        id:data.id
    }).then((result) => {
        if (result.data) {
            ElMessage.success('删除成功')
            emits('reload')
        }else{
            ElMessage.error('删除失败')
        }
    }).catch((err) => {
        
    });
}
function handleDelete(row) {
    msgdialogRef.value.show({
        type:'edit',
        title:'提醒',
        msg:'确认删除该活动吗？',
        submitBtnText:"确认删除",
        submitAction:()=>{
            deleteAction(row)
        }
    })
}
function handleCommand(command,row){
    if (command == 1) {
        router.push({
            path:'/jiaoyanshi/addActivity',
            query:{
                id:row.id
            }
        })
    }else if(command == 2){
        handleDelete(row)
    }else if (command == 3){
        // 开播
        liveStreamRef.value.show()
    }else if (command == 4){
        // 结束
        msgdialogRef.value.show({
            type:'edit',
            title:'提醒',
            msg:'确定要结束直播么？',
            submitBtnText:"确定",
            submitAction:()=>{
                console.log('cccccc====',row);
            }
        })
    }else if(command == 5){
        // 报名人员
        userjoinRef.value.show()
    }else if(command == 6){
        addplanbackRef.value.show()
    }
}
</script>

<style lang="scss" scoped>
.tagcss{
    padding: 4px 12px;
    border-radius: 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
    position: absolute;
    top: 8px;
    left: 8px;
}
.activity{
    background-color: #FFFFFF;
    position: relative;
}
.activity img{
    width: 100%;
    aspect-ratio: 16/9;
    overflow: hidden;
    border-radius: 8px !important;
}
.titlediv{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin:8px 8px  8px 8px;
    .title{
        width: calc(100% - 30px);
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #44474D;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
.titlediv img{
    width: 24px;
    height: 24px;
}
.msgdiv{
    margin:0px 8px  20px 8px;
    p{
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #878D99;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 8px;
        display: block !important;
    }
    
}
</style>