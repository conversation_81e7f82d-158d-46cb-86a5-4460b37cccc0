<template>
  <div class="jiaoyanitem">
    <img :src="item.coverUrl" alt="">
    <div class="titlediv">
        <el-text truncated class="title">{{item.name}}</el-text>
        <el-dropdown
         :hide-on-click="true"
         trigger="click"
        @command="handleCommand">
            <img src="@/assets/center/mbtnicon.png" alt="">
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item :command="1">管理</el-dropdown-item>
                    <el-dropdown-item :command="2">编辑</el-dropdown-item>
                    <el-dropdown-item :command="3">删除</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
    <div class="msgdiv">
        <el-text truncated tag="p">主办单位：{{item.organizer}}</el-text>
        <el-text truncated tag="p">专业大类：{{item.specialName || '--'}}</el-text>
        <el-text truncated tag="p">层次：{{item.levelName}}</el-text>
    </div>
    <msg-dialog ref="msgdialogRef" />
  </div>
</template>

<script setup>
import { deleteTeachingOffice } from "@/api/expert/index";
import { ElMessage,ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter()
const props = defineProps({
    item:{
        type:Object,
        default:()=>{
            return new Object()
        }
    }
})
const emits = defineEmits(['reload','toEdit'])
const msgdialogRef = ref()
function handleDelete(row) {
    console.log('ddlksda;sa;sl;');
    msgdialogRef.value.show({
        type:'edit',
        title:'删除',
        msg:'确认删除该教研室吗？',
        submitBtnText:"确认删除",
        submitAction:()=>{
            deleteTeachingOffice({id: row.id}).then(res => {
                if (res.data) {
                    ElMessage.success('删除成功');
                    emits('reload')
                }else{
                    ElMessage.error('删除失败')
                }
            
            })
        }
    })
}
function handleCommand(command){
    if (command == 1) {
        router.push({
            path:'/center/jysdetail/material',
            query:{
                id:props.item.id
            }
        })
    }else if(command == 2){
        emits('toEdit',props.item)
    }else if(command == 3){
         handleDelete(props.item)
    }
}
</script>

<style lang="scss" scoped>
.jiaoyanitem{
    background-color: #FFFFFF;
    
}
.jiaoyanitem img{
    width: 100%;
    aspect-ratio: 16/9;
    border-radius: 4px;
}
.titlediv{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin:12px 8px  8px 8px;
    .title{
        width: calc(100% - 30px);
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #44474D;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
.titlediv img{
    width: 24px;
    height: 24px;
}
.msgdiv{
    margin:0px 8px  20px 8px;
    p{
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #878D99;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 8px;
        display: block !important;
    }
    
}
</style>