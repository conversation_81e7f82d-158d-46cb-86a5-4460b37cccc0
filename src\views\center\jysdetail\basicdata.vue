<template>
  <div class="maskbg">
    <div class="titlediv">
      <div class="title">数据管理</div>
    </div>
    <el-form class="myform" label-width="80px">
        <el-form-item label="知识点:">
            <el-input-number 
            class="definput"
            :controls="false" 
            v-model="form.knowledgePoints"
            placeholder="请输入知识点数量"
            />
        </el-form-item>
        <el-form-item label="知识图谱:">
            <el-input-number 
            class="definput"
            :controls="false" 
            v-model="form.knowledgeGraph"
            placeholder="请输入知识图谱数量"
            />
        </el-form-item>
        <el-form-item label="能力图片:">
            <el-input-number 
            class="definput"
            :controls="false" 
            v-model="form.capabilityGraph"
            placeholder="请输入能力图片数量"
            />
        </el-form-item>
        <el-form-item>
            <el-button class="defbtn pd" @click="submitAction">立即保存</el-button>
        </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { saveBasicData,getBasicDataInfo } from '@/api/expert/basicdata';
import { ElMessage } from 'element-plus';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
let form = reactive({
    capabilityGraph:undefined,
    knowledgeGraph:undefined,
    knowledgePoints:undefined,
    researchOfficeId:route.query.id
})
const loadInfo = () =>{
  getBasicDataInfo(route.query.id).then((result) => {
    if (result.data) {
      form.capabilityGraph = result.data.capabilityGraph
      form.knowledgeGraph = result.data.knowledgeGraph
      form.knowledgePoints = result.data.knowledgePoints
      form.id = result.data.id
    }
  }).catch((err) => {
    
  });
}
const submitAction = () =>{
  saveBasicData(form).then((result) => {
    if (result.data) {
      ElMessage.success('保存成功')
      loadInfo()
    }
  }).catch((err) => {
    
  });
}
onMounted(()=>{
  loadInfo()
})
</script>

<style lang="scss" scoped>
.myform{
    margin-top: 20px;
}
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.definput{
  width: 280px;
  height: 36px;
  border-radius: 4px;
  ::v-deep(.el-input__inner){
    text-align: left !important;
  }
}
.pd{
    margin-top: 20px;
    padding: 9px 48px !important;
}
</style>