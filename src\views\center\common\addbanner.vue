<template>
    <el-dialog 
    width="493px" 
    class="adddialog"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
    :title="dialogTitle" 
    v-model="dialogVisible" 
    @close="close" 
    :close-on-click-modal="false">
        <el-form :rules="rules" ref="addform" :model="form" label-width="100px">
                <!-- <el-form-item label="标题" prop="name">
                    <el-input v-model="form.name" placeholder="请输入标题" show-word-limit maxlength="50" clearable></el-input>
                </el-form-item> -->
                <el-form-item label="轮播图" prop="imagsUrl">
                    <el-upload
                        class="upload-demo"
                        :action="getUrl"
                        name="file"
                        :limit="1"
                        accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
                        :on-remove="handleRemove"
                        :file-list="fileList"
                        :headers="headerUrl"
                        :on-success="handleAvatarSuccess"
                        :data="imgDetails"
                        list-type="picture"
                    >
                        <el-button class="defbtn">点击上传</el-button>
                        <template #tip >
                            <div class="el-upload__tip">
                                <p>PC端推荐1920*480(4:1)</p>
                                <p>只能上传jpg/png文件，且不超过10M</p>
                                
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
                <el-form-item label="跳转方式">
                    <el-radio-group v-model="form.jumpMode" @change="changeJumpMode">
                        <el-radio :value="1">无跳转</el-radio>
                        <el-radio :value="2">链接</el-radio>
                    </el-radio-group>
                </el-form-item>
                
                <el-form-item v-if="form.jumpMode == 2" label="跳转链接" prop="skipUrlPortal">
                    <el-input v-model="form.skipUrlPortal" placeholder="请输入跳转链接" clearable></el-input>
                </el-form-item>
            </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button class="linebtn" @click="close">取 消</el-button>
                <el-button class="defbtn40" @click="onSubmit">确定</el-button>
            </div>
        </template>
        
    </el-dialog>
</template>

<script setup>
import { 
    saveBanner,
    getBannerInfo, 
    updateBanner} from '@/api/expert/banner'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const dialogVisible = ref(false)
const headerUrl = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const imgDetails = ref({ serviceName: 'web' })
const dialogTitle = ref('新增轮播图')
let form = reactive({
    id: '',
    name: '',
    jumpMode:1,
    skipUrlPortal: '',
    imagsUrl:'',
    coverFileSize:'',
    coverFileName:'',
    commonId:route.query.id
})
const rules = ref({
    name: [
        { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    imagsUrl: [
        { required: true, message: '请上传轮播图', trigger: 'blur' }
    ],
    skipUrlPortal: [
        { 
            pattern: /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{1,64})?\.)+[a-z]{2,6}\/?/, 
            message: '请输入正确的链接格式', 
            trigger: 'blur' 
        }
    ]
})
const emits = defineEmits(['reload'])
const addform = ref()
const loadInfo = () =>{
    getBannerInfo(form.id).then((result) => {
        Object.keys(form).forEach(key => {
            form[key] = result.data[key]
        });
        fileList.value = [{
            url:form.imagsUrl,
            name:form.coverFileName,
            size:form.coverFileSize
        }]
    }).catch((err) => {
        
    });
}
const show = (id) =>{
    if (id) {
        dialogTitle.value = '编辑轮播图'
        form.id = id;
        loadInfo(id)
    }else{
        dialogTitle.value = '新增轮播图'
    }
    dialogVisible.value = true;
}
function resetForm() {
    fileList.value = [];
    for (let key in form) {
        if (key == 'jumpMode') {
            form[key] = 1
        }else if(key != 'commonId'){
            form[key] = '';
        }
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
function changeJumpMode(){
    
    if (form.jumpMode == 1) {
        form.skipUrlPortal = ''
    }
}
function onSubmit() {

    console.log(form);
    addform.value.validate((valid) => {
        if (valid) {
            const request = form.id ? 
                updateBanner(form) : 
                saveBanner(form);
            
            request.then(res => {
                if (res.status == 0) {
                    ElMessage.success(form.id ? '更新成功' : '保存成功');
                    dialogVisible.value = false;
                    emits('reload')
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}
function handleRemove(file, fileList) {
    fileList.value = fileList;
    form.imagsUrl = '';
    form.coverFileName = '';
    form.coverFileSize = '';
}
function handlePreview(file) {
    
}
function handleAvatarSuccess(res, file, fileList) {
    fileList.value = fileList;
    if (res.status == 0) {
        form.imagsUrl = res.data.url;
        form.coverFileName = res.data.fileName;
        form.coverFileSize = res.data.size;
    } else {
        ElMessage.error('上传失败：' + res.msg);
    }
}

onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo{
    // ::v-deep(.el-upload-list__item){
    //     width: calc(100% - 100px);
    // }
    // ::v-deep(.el-upload-list__item-info){
    //     width: calc(100% - 80px);
    // }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>

<style lang="scss">
.upload-demo{
    width: 100%;
    max-width: 100%;
}
</style>