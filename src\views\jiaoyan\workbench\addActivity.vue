<template>
    <div class="w1280 adddiv">
        <back>返回</back>
        <el-form class="addform" :rules="rules" ref="addform" :model="form" label-width="100px">
            <el-form-item label-width="0px">
                <div class="title">{{title}}</div>
            </el-form-item>
            <el-form-item label="活动名称" prop="name">
                <el-input 
                class="definput"
                v-model="form.name" 
                placeholder="请输入活动名称" 
                show-word-limit 
                maxlength="20" 
                clearable></el-input>
            </el-form-item>
            <el-form-item label="活动类型" prop="type">
                <el-select 
                v-model="form.type" 
                class="definput"
                placeholder="请选择活动类型" 
                clearable>
                    <el-option 
                    v-for="item in activityTypes" 
                    :key="item.id" 
                    :label="item.name" 
                    :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="活动封面" prop="coverUrl">
                <el-upload
                    class="upload-demo"
                    :action="getUrl"
                    name="file"
                    :limit="1"
                    accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :file-list="fileList"
                    :headers="headerUrl"
                    :on-success="handleAvatarSuccess"
                    :data="imgDetails"
                    list-type="picture"
                >
                    <el-button class="defbtn">点击上传封面</el-button>
                    <template #tip>
                        <div class="el-upload__tip">只能上传jpg/png文件，且不超过2M</div>
                    </template>
                </el-upload>
            </el-form-item>
                            
            <el-form-item label="简介" prop="intro">
                <el-input 
                class="definput"
                v-model="form.intro" 
                placeholder="请输入" 
                type="textarea"
                show-word-limit 
                :rows="3"
                maxlength="200" clearable></el-input>
            </el-form-item>
            <el-form-item label="时间" prop="time" >
                <el-input 
                class="definput" 
                type="textarea" 
                v-model="form.time" 
                :rows="3" 
                placeholder="请输入" 
                show-word-limit 
                maxlength="100"></el-input>
            </el-form-item>
            <el-form-item label="主讲人" prop="speaker">
                <el-input 
                class="definput"
                v-model="form.speaker" 
                placeholder="请输入主讲人姓名" 
                show-word-limit 
                maxlength="20" 
                clearable></el-input>
            </el-form-item>
            <el-form-item label="更多介绍" prop="notes">
                <editor ref="myeditorRef" />
            </el-form-item>
            <el-form-item>
                <el-button class="defbtn40" @click="onSubmit">保存</el-button>
                <el-button class="linebtn" @click="onClose">取消</el-button>
            </el-form-item>
        </el-form>
    </div>
    
</template>

<script setup>
import { 
    saveActivity,
    updateActivity,
    getActivityInfo
} from '@/api/expert/index'
import {
     labelList, 
} from '@/api/api'
import editor from '@/components/editor.vue';
import { useRouter,useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
import { activityTypes } from '@/utils/static-tools';
const router = useRouter()
const title = ref('新增活动')
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const headerUrl = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const imgDetails = ref({ serviceName: 'web' })
const myeditorRef = ref()
let form = reactive({
        id: '',
        name: '',
        coverUrl: '',
        coverName: '',
        coverSize: '',
        type: '',
        time:'',
        speaker: '',
        intro: '',
        notes:'',
        researchOfficeId:route.query.researchOfficeId
    })
const rules = ref({
    name: [
        { required: true, message: '请输入活动名称', trigger: 'blur' }
    ],
    coverUrl: [
        { required: true, message: '请上传活动封面', trigger: 'blur' }
    ],
    type: [
        { required: true, message: '请选择活动类型', trigger: 'change' }
    ],
    speaker: [
        { required: true, message: '请输入主讲人', trigger: 'blur' }
    ],
    time: [
        { required: true, message: '请输入主讲人', trigger: 'blur' }
    ],
    intro: [
        { required: true, message: '请输入简介', trigger: 'blur' }
    ],
})
const addform = ref()
const loadInfo = () =>{
    getActivityInfo(route.query.id).then((result) => {
        Object.keys(form).forEach(key => {
            form[key] = result.data[key]
        });
        fileList.value = [
            {
                url:form.coverUrl,
                name:form.coverName,
                size:form.coverSize
            }
        ]
        myeditorRef.value.setHtml(form.notes)
    }).catch((err) => {
        
    });
}

function resetForm() {
    fileList.value = [];
    for (let key in form) {
        if (key != 'researchOfficeId') {
             form[key] = '';
        }
    }
}

function onSubmit() {
    form.notes = myeditorRef.value.getContentHtml
    addform.value.validate((valid) => {
        if (valid) {
            const request = form.id ? 
                updateActivity(form) : 
                saveActivity(form);
            
            request.then(res => {
                if (res.status == 0) {
                    ElMessage.success(form.id ? '更新成功' : '保存成功');
                    router.go(-1)
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}
function handleRemove(file, fileList) {
    fileList.value = fileList;
    form.coverUrl = '';
    form.coverName = '';
    form.coverSize = '';
}
function handlePreview(file) {
    
}
function handleAvatarSuccess(res, file, fileList) {
    fileList = fileList;
    if (res.status == 0) {
        form.coverUrl = res.data.url;
        form.coverName = res.data.fileName;
        form.coverSize = res.data.size;
    } else {
        ElMessage.error('上传失败：' + res.msg);
    }
}
onMounted(()=>{
    if (route.query.id) {
        title.value = '编辑活动'
        loadInfo()
    }else{
        title.value = '新增活动'
    }
})
</script>

<style lang="scss" scoped>
.adddiv{
    padding: 20px 0;
}
.title{
    height: 30px;
    line-height: 30px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
}
.addform{
    margin-top: 20px;
    background-color: #FFFFFF;
    padding: 30px;
}
.upload-demo {
     width:280px !important; 
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>