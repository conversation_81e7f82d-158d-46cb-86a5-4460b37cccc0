<template>
  <material :type="1"></material>
</template>

<script setup>
import material from '../material.vue';
</script>

<style lang="scss" scoped>
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source <PERSON>, Source <PERSON>s CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
</style>