<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <div class="bookList">
                <div class="bookItem" v-for="(item,index) in bookList" :key="index">
                    <img :src="item.coverUrl" alt="" class="bookImg">
                    <div class="name text-ellipsis-1">{{ item.name }}</div>
                    <div class="time">
                        <img src='@/assets/time.png' alt="" class="userImg">
                        <span class="text">{{ item.createTime }}</span>
                    </div>
                </div>
            </div>
            <el-pagination
                v-model:current-page="pageParams.pageNum"
                v-model:page-size="pageParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                style="margin-top: 20px; justify-content: center;"
            />
        </div>
    </div>
</template>
    
<script setup>
import { useRoute } from 'vue-router'
const route = useRoute()
import { getMaterialList } from '@/api/expert/material'
const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
    type:1,
    relateId:route.query.id
})
const total = ref(0)

const bookList =ref([])

const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
    fetchBookList()
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
    fetchBookList()
}

const fetchBookList = () => {
    getMaterialList(pageParams.value).then(res => {
        bookList.value = res.data
        total.value = res.page.total
    })
}

onMounted(() => {
    fetchBookList()
});

</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  height: 100vh;
  padding: 31px;
}
.bookList{
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 20px;
}
.bookItem{
    width: 240px;
    height: 358px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 24px;
    padding-bottom: 16px;
}
.bookImg{
    width: 192px;
    height: 254px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #E1E4EB;
}
.time{
    display: flex;
    align-items: center;
}
.name{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2D2F33;
    margin-top: 12px;
    margin-bottom: 8px;
}
.userImg{
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.text{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
}
</style>
  