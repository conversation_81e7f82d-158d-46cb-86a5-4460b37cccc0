import service from '@/utils/request.js'

// 保存智慧教研室资源信息
export function saveResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/aigcresourcedetail/save',
    data,
  });
}

// 删除智慧教研室资源信息
export function deleteResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/aigcresourcedetail/delete',
    data,
  });
}

// 获取智慧教研室资源详情
export function getResourceInfo(id) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/aigcresourcedetail/info/${id}`,
  });
}

// 更新智慧教研室资源信息
export function updateResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/aigcresourcedetail/update',
    data,
  });
}
