import service from '@/utils/request.js'
export function exlertLiveList(data) {
  return service.request({
    method: 'get',
    url: '/ear/portal/courseinfo/list',
    params: data
  });
}

export function offLectureList(data) {
  return service.request({
    method: 'get',
    url: '/aigc/business/activity/list',
    params: data
  });
}
// 教研室列表
export function jiaoyanlist(data) {
  return service.request({
    method: 'get',
    url: '/aigc/controller/teachingresearchoffice/list',
    params: data
  });
}
//新增教研室
export function saveTeachingOffice(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/teachingresearchoffice/save',
    data,
  });
}

// 更新教研室
export function updateTeachingOffice(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/teachingresearchoffice/update',
    data,
  });
}
// 教研室详情
export function jyinfo(id){
  return service.request({
    method: 'get',
    url: `/aigc/controller/teachingresearchoffice/info/${id}`,
  });
}
// 教研室删除
export function deleteTeachingOffice(data){
  return service.request({
    method: 'post',
    url: '/aigc/controller/teachingresearchoffice/delete',
    data,
    type:2,
  });
}
//  教研室-资源分类删除
export function deleteResourcetype(data){
  return service.request({
    method: 'post',
    url: '/aigc/research/resourcetype/delete',
    data,
    type:2,
  });
}
// 教研室-资源分类列表
export function getResTypeList(params){
  return service.request({
    method: 'get',
    url: '/aigc/research/resourcetype/list',
    params
  });
}
// 教研室-资源分类详情
export function getResTypeInfo(id){
  return service.request({
    method: 'get',
    url: `/aigc/research/resourcetype/info/${id}`,
  });
}
// 教研室-资源分类保存
export function saveResourcetype(data){
  return service.request({
    method: 'post',
    url: '/aigc/research/resourcetype/save',
    data,
  });
}
// 教研室-资源分类编辑
export function updateResourcetype(data){
  return service.request({
    method: 'post',
    url: '/aigc/research/resourcetype/update',
    data,
  });
}
//教研室资源管理列表
export function resourceList(params) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/aigcresourcedetail/list',
	  params
	});
}

// 保存智慧教研室资源信息
export function saveTeachingResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/aigcresourcedetail/save',
    data,
  });
}

// 删除智慧教研室资源信息
export function deleteTeachingResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/aigcresourcedetail/delete',
    data,
  });
}

// 获取智慧教研室资源详情
export function getResourceInfo(id) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/aigcresourcedetail/info/${id}`,
  });
}

// 更新智慧教研室资源信息
export function updateTeachingResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/aigcresourcedetail/update',
    data,
  });
}

export function offLectureInfo(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/expertchairinfo/info?id=${data.id}&userId=${data.userId}`,
  });
}
export function xianxiaInfo(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/expertchairinfo/info?id=${data.id}`,
  });
}
export function jiaoyanInfo(data) {
  return service.request({
    method: 'get',
    url: `/aigc/business/activity/info?id=${data.id}`,
  });
}
// 教研室详情
export function jiaoyanDetail(data) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/teachingresearchoffice/info/${data.id}`,
  });
}
// 教研室详情统计信息
export function jiaoyanDetailInfo(data) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/countInfo/getCountInfo/${data.id}`,
  });
}

export function offLectureSave(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/expertchairuserrelation/save',
    data
  });
}



// 获取教研室主理人列表
export function getResearchOfficeUsers(params) {
  return service.request({
    method: 'get',
    url: '/aigc/controller/resarchofficeuser/list',
    params
  });
}
// 获取教研室资源列表
export function getResourceList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/controller/aigcresourcedetail/list',
    params,
  });
}
// 保存资源
export function saveResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/resourcedetail/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 申请加入教研室
export function applyJoinResearchOffice(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/resarchofficeuser/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


// 更新教研室用户状态
export function updateOfficeUser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/teachingresearchoffice/updateOfficeUser',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 主理人详情
export function getUserInfo(data) {
  return service.request({
    method: 'post',
    url: `/aigc/business/userlogin/info`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

//获取教研室用户角色信息
export function getResarchOfficeUserInfo(id) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/resarchofficeuser/info/${id}`,
  });
}
export function getPersonInf(data) {
  return service.request({
    method: 'post',
    url: `/aigc/business/userlogin/info`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 关注用户
export function followUser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/fans/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 取消关注用户
export function unfollowUser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/fans/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 获取关注和粉丝数量
export function getFansCount(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/fans/getCount',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取资源详情
export function getResourceDetail(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/resourcedetail/info/${id}`,
  });
}

// 更新资源
export function updateResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/resourcedetail/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除资源
export function deleteResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/resourcedetail/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取我的教材列表
export function getMyBookList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/business/materialinfo/list',
    params,
  });
}

// 保存教材
export function saveMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/materialinfo/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取教材详情
export function getMaterialDetail(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/materialinfo/info/${id}`,
  });
}

// 更新教材
export function updateMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/materialinfo/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除教材
export function deleteMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/materialinfo/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 教研室-获取活动列表
export function getActivityList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/controller/activity/list',
    params
  });
}

// 教研室-保存活动
export function saveActivity(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/activity/save',
    data,
  });
}

// 教研室-更新活动
export function updateActivity(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/activity/update',
    data,
  });
}

// 教研室-活动删除
export function deleteActivity(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/activity/delete',
    data,
    type:2,
  });
}

// 教研室-获取活动详情
export function getActivityInfo(id) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/activity/info/${id}`,
  });
}

// 教研室-人员审核/授权
export function updateResarchofficeuser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/resarchofficeuser/update',
    data,
  });
}